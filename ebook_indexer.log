2025-05-30 17:23:12 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-30 17:23:17 - ebook_indexer.database.connection.MongoDBConnection - ERROR - Failed to connect to MongoDB: localhost:27017: [Errno 104] Connection reset by peer (configured timeouts: socketTimeoutMS: 5000.0ms, connectTimeoutMS: 5000.0ms), Timeout: 5.0s, Topology Description: <TopologyDescription id: 6839cd604ca2e907c537093e, topology_type: Unknown, servers: [<ServerDescription ('localhost', 27017) server_type: Unknown, rtt: None, error=AutoReconnect('localhost:27017: [Errno 104] Connection reset by peer (configured timeouts: socketTimeoutMS: 5000.0ms, connectTimeoutMS: 5000.0ms)')>]>
2025-05-30 17:23:17 - ebook_indexer.database.connection.MongoDBConnection - WARNING - MongoDB health check failed: Failed to connect to MongoDB: localhost:27017: [<PERSON>rrno 104] Connection reset by peer (configured timeouts: socketTimeoutMS: 5000.0ms, connectTimeoutMS: 5000.0ms), Timeout: 5.0s, Topology Description: <TopologyDescription id: 6839cd604ca2e907c537093e, topology_type: Unknown, servers: [<ServerDescription ('localhost', 27017) server_type: Unknown, rtt: None, error=AutoReconnect('localhost:27017: [Errno 104] Connection reset by peer (configured timeouts: socketTimeoutMS: 5000.0ms, connectTimeoutMS: 5000.0ms)')>]>
2025-05-30 17:23:24 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-30 17:23:24 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-30 17:23:32 - ebook_indexer.core.scanner.DirectoryScanner - INFO - Scanner initialized with extensions: ['.pdf', '.epub', '.mobi', '.azw', '.azw3']
2025-05-30 17:23:32 - ebook_indexer.core.metadata_extractor.MetadataExtractor - INFO - Metadata extractor initialized for formats: ['.pdf', '.epub']
2025-05-30 17:23:32 - ebook_indexer.core.anomaly_detector.AnomalyDetector - INFO - Anomaly detector initialized with config: {'max_nesting_depth': 4, 'enforce_naming_convention': True, 'detect_misplaced_files': True, 'severity_thresholds': {'wrong_level': 'medium', 'deep_nesting': 'low', 'missing_directory': 'high', 'naming_violation': 'low'}}
2025-05-30 17:23:32 - ebook_indexer.core.indexer.EbookIndexer - INFO - Ebook indexer initialized
2025-05-30 17:32:30 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-30 17:32:30 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-30 17:41:09 - ebook_indexer.database.connection.MongoDBConnection - INFO - Disconnecting from MongoDB
2025-05-30 17:41:21 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-30 17:41:21 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-30 17:47:17 - ebook_indexer.database.connection.MongoDBConnection - INFO - Disconnecting from MongoDB
2025-05-30 17:47:17 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-30 17:47:17 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-30 17:47:31 - ebook_indexer.database.connection.MongoDBConnection - INFO - Disconnecting from MongoDB
2025-05-30 17:47:31 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-30 17:47:31 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-30 17:47:38 - ebook_indexer.database.connection.MongoDBConnection - INFO - Disconnecting from MongoDB
2025-05-30 17:47:39 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-30 17:47:39 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-30 17:48:04 - ebook_indexer.database.connection.MongoDBConnection - INFO - Disconnecting from MongoDB
2025-05-30 17:48:04 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-30 17:48:04 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-30 17:48:12 - ebook_indexer.database.connection.MongoDBConnection - INFO - Disconnecting from MongoDB
2025-05-30 17:48:12 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-30 17:48:12 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-30 17:48:22 - ebook_indexer.database.connection.MongoDBConnection - INFO - Disconnecting from MongoDB
2025-05-30 17:48:22 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-30 17:48:22 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-30 17:48:50 - ebook_indexer.database.connection.MongoDBConnection - INFO - Disconnecting from MongoDB
2025-05-30 17:48:50 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-30 17:48:50 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-30 17:49:53 - ebook_indexer.database.connection.MongoDBConnection - INFO - Disconnecting from MongoDB
2025-05-30 17:49:54 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-30 17:49:54 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-30 17:50:15 - ebook_indexer.database.connection.MongoDBConnection - INFO - Disconnecting from MongoDB
2025-05-30 17:50:15 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-30 17:50:15 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-30 17:50:26 - ebook_indexer.database.connection.MongoDBConnection - INFO - Disconnecting from MongoDB
2025-05-30 17:50:26 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-30 17:50:26 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-30 17:50:41 - ebook_indexer.database.connection.MongoDBConnection - INFO - Disconnecting from MongoDB
2025-05-30 17:50:41 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-30 17:50:41 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-30 17:50:48 - ebook_indexer.database.connection.MongoDBConnection - INFO - Disconnecting from MongoDB
2025-05-30 17:50:49 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-30 17:50:49 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-30 17:51:14 - ebook_indexer.database.connection.MongoDBConnection - INFO - Disconnecting from MongoDB
2025-05-30 17:51:14 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-30 17:51:14 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-30 17:51:32 - ebook_indexer.database.connection.MongoDBConnection - INFO - Disconnecting from MongoDB
2025-05-30 17:51:32 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-30 17:51:32 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-30 17:51:49 - ebook_indexer.database.connection.MongoDBConnection - INFO - Disconnecting from MongoDB
2025-05-30 17:51:50 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-30 17:51:50 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-30 17:51:58 - ebook_indexer.database.connection.MongoDBConnection - INFO - Disconnecting from MongoDB
2025-05-30 17:51:59 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-30 17:51:59 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-30 17:52:20 - ebook_indexer.database.connection.MongoDBConnection - INFO - Disconnecting from MongoDB
2025-05-30 17:52:20 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-30 17:52:20 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-30 17:52:42 - ebook_indexer.database.connection.MongoDBConnection - INFO - Disconnecting from MongoDB
2025-05-30 17:52:43 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-30 17:52:43 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-30 17:52:59 - ebook_indexer.database.connection.MongoDBConnection - INFO - Disconnecting from MongoDB
2025-05-30 17:52:59 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-30 17:52:59 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-30 17:53:37 - ebook_indexer.database.connection.MongoDBConnection - INFO - Disconnecting from MongoDB
2025-05-30 17:53:37 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-30 17:53:37 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-30 17:53:56 - ebook_indexer.database.connection.MongoDBConnection - INFO - Disconnecting from MongoDB
2025-05-30 17:53:56 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-30 17:53:56 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-30 17:55:10 - ebook_indexer.database.connection.MongoDBConnection - INFO - Disconnecting from MongoDB
2025-05-30 17:55:10 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-30 17:55:10 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-30 17:55:21 - ebook_indexer.database.connection.MongoDBConnection - INFO - Disconnecting from MongoDB
2025-05-30 17:55:21 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-30 17:55:21 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-30 17:55:29 - ebook_indexer.database.connection.MongoDBConnection - INFO - Disconnecting from MongoDB
2025-05-30 17:55:30 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-30 17:55:30 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-30 17:56:12 - ebook_indexer.database.connection.MongoDBConnection - INFO - Disconnecting from MongoDB
2025-05-30 17:56:12 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-30 17:56:12 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-30 17:56:49 - ebook_indexer.database.connection.MongoDBConnection - INFO - Disconnecting from MongoDB
2025-05-30 17:56:49 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-30 17:56:49 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-30 17:57:21 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-30 17:57:21 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-30 17:57:21 - ebook_indexer.database.connection.MongoDBConnection - INFO - Disconnecting from MongoDB
2025-05-30 17:59:01 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-30 17:59:01 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-30 18:00:31 - ebook_indexer.core.scanner.DirectoryScanner - INFO - Scanner initialized with extensions: ['.pdf', '.epub', '.mobi', '.azw', '.azw3', '.txt']
2025-05-30 18:00:31 - ebook_indexer.core.metadata_extractor.MetadataExtractor - INFO - Metadata extractor initialized for formats: ['.pdf', '.epub']
2025-05-30 18:00:31 - ebook_indexer.core.anomaly_detector.AnomalyDetector - INFO - Anomaly detector initialized with config: {'max_nesting_depth': 4, 'enforce_naming_convention': True, 'detect_misplaced_files': True, 'severity_thresholds': {'wrong_level': 'medium', 'deep_nesting': 'low', 'missing_directory': 'high', 'naming_violation': 'low'}}
2025-05-30 18:00:31 - ebook_indexer.core.indexer.EbookIndexer - INFO - Ebook indexer initialized
2025-05-30 18:01:13 - ebook_indexer.database.connection.MongoDBConnection - INFO - Disconnecting from MongoDB
2025-05-30 18:01:14 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-30 18:01:14 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-30 18:03:27 - ebook_indexer.database.connection.MongoDBConnection - INFO - Disconnecting from MongoDB
2025-05-30 18:03:28 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-30 18:03:28 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-30 18:03:35 - ebook_indexer.core.scanner.DirectoryScanner - INFO - Scanner initialized with extensions: ['.pdf', '.epub', '.mobi', '.azw', '.azw3', '.txt']
2025-05-30 18:03:35 - ebook_indexer.core.metadata_extractor.MetadataExtractor - INFO - Metadata extractor initialized for formats: ['.pdf', '.epub']
2025-05-30 18:03:35 - ebook_indexer.core.anomaly_detector.AnomalyDetector - INFO - Anomaly detector initialized with config: {'max_nesting_depth': 4, 'enforce_naming_convention': True, 'detect_misplaced_files': True, 'severity_thresholds': {'wrong_level': 'medium', 'deep_nesting': 'low', 'missing_directory': 'high', 'naming_violation': 'low'}}
2025-05-30 18:03:35 - ebook_indexer.core.indexer.EbookIndexer - INFO - Ebook indexer initialized
2025-05-30 18:07:20 - ebook_indexer.database.connection.MongoDBConnection - INFO - Disconnecting from MongoDB
2025-05-30 18:07:22 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-30 18:07:22 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-30 18:07:49 - ebook_indexer.core.scanner.DirectoryScanner - INFO - Scanner initialized with extensions: ['.pdf', '.epub', '.mobi', '.azw', '.azw3', '.txt']
2025-05-30 18:07:49 - ebook_indexer.core.metadata_extractor.MetadataExtractor - INFO - Metadata extractor initialized for formats: ['.pdf', '.epub']
2025-05-30 18:07:49 - ebook_indexer.core.anomaly_detector.AnomalyDetector - INFO - Anomaly detector initialized with config: {'max_nesting_depth': 4, 'enforce_naming_convention': True, 'detect_misplaced_files': True, 'severity_thresholds': {'wrong_level': 'medium', 'deep_nesting': 'low', 'missing_directory': 'high', 'naming_violation': 'low'}}
2025-05-30 18:07:49 - ebook_indexer.core.indexer.EbookIndexer - INFO - Ebook indexer initialized
2025-05-30 18:07:49 - ebook_indexer.core.indexer.EbookIndexer - INFO - Skipping 3 already indexed directories: ['/run/user/1000/gvfs/smb-share:server=**************,share=dmedia6/scanlibs-ebooks-202402', '/run/user/1000/gvfs/smb-share:server=**************,share=dmedia6/scanlibs-ebooks-202403', '/run/user/1000/gvfs/smb-share:server=**************,share=dmedia6/scanlibs-ebooks-202404']
2025-05-30 18:07:49 - ebook_indexer.core.indexer.EbookIndexer - INFO - Use --force flag to reindex these directories
2025-05-30 18:07:49 - ebook_indexer.core.indexer.EbookIndexer - INFO - Created new job 6839d7d5dced5e6b6b85f6af
2025-05-30 18:07:49 - ebook_indexer.core.indexer.EbookIndexer - INFO - Starting indexing job 6839d7d5dced5e6b6b85f6af for directories: ['/run/user/1000/gvfs/smb-share:server=**************,share=dmedia6/scanlibs-ebooks-202405']
2025-05-30 18:07:55 - ebook_indexer.core.indexer.EbookIndexer - INFO - Found 376 files to process
2025-05-30 18:07:55 - ebook_indexer.utils.progress_tracker.ProgressTracker - INFO - Progress tracker initialized: Indexing 1 directories (skipped 3 already indexed) (376 items)
2025-05-30 18:07:55 - ebook_indexer.core.scanner.DirectoryScanner - INFO - Scanning directory: /run/user/1000/gvfs/smb-share:server=**************,share=dmedia6/scanlibs-ebooks-202405
2025-05-30 18:07:55 - ebook_indexer.core.scanner.DirectoryScanner - INFO - Starting scan of directory: /run/user/1000/gvfs/smb-share:server=**************,share=dmedia6/scanlibs-ebooks-202405
2025-05-30 18:07:56 - ebook_indexer.utils.progress_tracker.ProgressTracker - INFO - Started: Indexing 1 directories (skipped 3 already indexed)
2025-05-30 18:07:56 - ebook_indexer.core.indexer.EbookIndexer - INFO - Indexing 1 directories (skipped 3 already indexed): 0/376 (0.0%)
2025-05-30 18:08:33 - ebook_indexer.core.indexer.EbookIndexer - INFO - Indexing 1 directories (skipped 3 already indexed): 50/376 (13.3%) - 1.35 items/sec - ETA: 4m 2s
2025-05-30 18:09:15 - ebook_indexer.core.indexer.EbookIndexer - INFO - Indexing 1 directories (skipped 3 already indexed): 100/376 (26.6%) - 1.26 items/sec - ETA: 3m 38s
2025-05-30 18:10:20 - ebook_indexer.core.indexer.EbookIndexer - INFO - Indexing 1 directories (skipped 3 already indexed): 250/376 (66.5%) - 1.73 items/sec - ETA: 1m 12s
2025-05-30 18:10:26 - ebook_indexer.core.indexer.EbookIndexer - WARNING - Metadata extraction failed for foundations-quantum-programming-2nd.epub: Error reading EPUB metadata: "There is no item named 'OEBPS/images/B9780443159428000095/si10.png' in the archive"
2025-05-30 18:11:23 - ebook_indexer.core.indexer.EbookIndexer - INFO - Indexing 1 directories (skipped 3 already indexed): 350/376 (93.1%) - 1.69 items/sec - ETA: 0m 15s
2025-05-30 18:11:36 - ebook_indexer.utils.progress_tracker.ProgressTracker - INFO - Completed: Indexing 1 directories (skipped 3 already indexed) - 376/376 items (100.0%) in 0:03:40.533881 (1.70 items/sec)
2025-05-30 18:11:43 - ebook_indexer.core.indexer.EbookIndexer - WARNING - Metadata extraction failed for geophysical-analysis-matlab-python-5th.pdf: Error reading PDF metadata: unhashable type: 'ArrayObject'
2025-05-30 18:11:50 - ebook_indexer.core.indexer.EbookIndexer - INFO - Indexing 1 directories (skipped 3 already indexed): 400/376 (106.4%) - 1.71 items/sec
2025-05-30 18:12:12 - ebook_indexer.core.indexer.EbookIndexer - INFO - Job 6839d7d5dced5e6b6b85f6af completed: 431/431 files successful, 0 failed, 59 with anomalies
2025-05-30 21:43:51 - ebook_indexer.database.connection.MongoDBConnection - INFO - Disconnecting from MongoDB
2025-05-30 21:43:53 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-30 21:43:53 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-30 21:44:20 - ebook_indexer.core.scanner.DirectoryScanner - INFO - Scanner initialized with extensions: ['.pdf', '.epub', '.mobi', '.azw', '.azw3', '.txt']
2025-05-30 21:44:20 - ebook_indexer.core.metadata_extractor.MetadataExtractor - INFO - Metadata extractor initialized for formats: ['.pdf', '.epub']
2025-05-30 21:44:20 - ebook_indexer.core.anomaly_detector.AnomalyDetector - INFO - Anomaly detector initialized with config: {'max_nesting_depth': 4, 'enforce_naming_convention': True, 'detect_misplaced_files': True, 'severity_thresholds': {'wrong_level': 'medium', 'deep_nesting': 'low', 'missing_directory': 'high', 'naming_violation': 'low'}}
2025-05-30 21:44:20 - ebook_indexer.core.indexer.EbookIndexer - INFO - Ebook indexer initialized
2025-05-30 21:44:20 - ebook_indexer.core.indexer.EbookIndexer - INFO - Skipping 4 already indexed directories: ['/run/user/1000/gvfs/smb-share:server=**************,share=dmedia6/scanlibs-ebooks-202402', '/run/user/1000/gvfs/smb-share:server=**************,share=dmedia6/scanlibs-ebooks-202403', '/run/user/1000/gvfs/smb-share:server=**************,share=dmedia6/scanlibs-ebooks-202404', '/run/user/1000/gvfs/smb-share:server=**************,share=dmedia6/scanlibs-ebooks-202405']
2025-05-30 21:44:20 - ebook_indexer.core.indexer.EbookIndexer - INFO - Use --force flag to reindex these directories
2025-05-30 21:44:20 - ebook_indexer.core.indexer.EbookIndexer - INFO - Created new job 683a0a9486468a8490a8d797
2025-05-30 21:44:20 - ebook_indexer.core.indexer.EbookIndexer - INFO - Starting indexing job 683a0a9486468a8490a8d797 for directories: ['/run/user/1000/gvfs/smb-share:server=**************,share=dmedia6/scanlibs-ebooks-202406']
2025-05-30 21:44:27 - ebook_indexer.core.indexer.EbookIndexer - INFO - Found 646 files to process
2025-05-30 21:44:27 - ebook_indexer.utils.progress_tracker.ProgressTracker - INFO - Progress tracker initialized: Indexing 1 directories (skipped 4 already indexed) (646 items)
2025-05-30 21:44:27 - ebook_indexer.core.scanner.DirectoryScanner - INFO - Scanning directory: /run/user/1000/gvfs/smb-share:server=**************,share=dmedia6/scanlibs-ebooks-202406
2025-05-30 21:44:27 - ebook_indexer.core.scanner.DirectoryScanner - INFO - Starting scan of directory: /run/user/1000/gvfs/smb-share:server=**************,share=dmedia6/scanlibs-ebooks-202406
2025-05-30 21:44:28 - ebook_indexer.utils.progress_tracker.ProgressTracker - INFO - Started: Indexing 1 directories (skipped 4 already indexed)
2025-05-30 21:44:28 - ebook_indexer.core.indexer.EbookIndexer - INFO - Indexing 1 directories (skipped 4 already indexed): 0/646 (0.0%)
2025-05-30 21:44:29 - ebook_indexer.core.indexer.EbookIndexer - WARNING - Metadata extraction failed for api-design-cpp-2nd.pdf: Error reading PDF metadata: unhashable type: 'ArrayObject'
2025-05-30 21:44:29 - ebook_indexer.core.indexer.EbookIndexer - WARNING - Metadata extraction failed for c-brain-exercise-mind.epub: Error reading EPUB metadata: "There is no item named 'OEBPS/images/aside-icons/important.png' in the archive"
2025-05-30 21:44:52 - ebook_indexer.core.indexer.EbookIndexer - INFO - Indexing 1 directories (skipped 4 already indexed): 50/646 (7.7%) - 2.15 items/sec - ETA: 4m 37s
2025-05-30 21:45:30 - ebook_indexer.core.indexer.EbookIndexer - WARNING - Metadata extraction failed for data-analysis-related-applications-3.epub: Error reading EPUB metadata: "There is no item named 'OEBPS/images/eqn10-1.jpg' in the archive"
2025-05-30 21:45:52 - ebook_indexer.core.indexer.EbookIndexer - INFO - Indexing 1 directories (skipped 4 already indexed): 150/646 (23.2%) - 1.79 items/sec - ETA: 4m 37s
2025-05-30 21:46:21 - ebook_indexer.core.indexer.EbookIndexer - INFO - Indexing 1 directories (skipped 4 already indexed): 200/646 (31.0%) - 1.78 items/sec - ETA: 4m 10s
2025-05-30 21:46:50 - ebook_indexer.core.indexer.EbookIndexer - INFO - Indexing 1 directories (skipped 4 already indexed): 250/646 (38.7%) - 1.76 items/sec - ETA: 3m 44s
2025-05-30 21:47:57 - ebook_indexer.core.indexer.EbookIndexer - INFO - Indexing 1 directories (skipped 4 already indexed): 350/646 (54.2%) - 1.68 items/sec - ETA: 2m 56s
2025-05-30 21:48:05 - ebook_indexer.core.indexer.EbookIndexer - WARNING - Metadata extraction failed for foundations-quantum-programming-2nd.epub: Error reading EPUB metadata: "There is no item named 'OEBPS/images/B9780443159428000095/si10.png' in the archive"
2025-05-30 21:48:52 - ebook_indexer.core.indexer.EbookIndexer - WARNING - Metadata extraction failed for agile-retrospectives-practical-2nd.epub: Error reading EPUB metadata: "There is no item named 'OEBPS/images/ActivitiesToDecideWhatToDo/image1.png' in the archive"
2025-05-30 21:48:56 - ebook_indexer.core.indexer.EbookIndexer - INFO - Indexing 1 directories (skipped 4 already indexed): 450/646 (69.7%) - 1.68 items/sec - ETA: 1m 56s
2025-05-30 21:49:28 - ebook_indexer.core.indexer.EbookIndexer - INFO - Indexing 1 directories (skipped 4 already indexed): 500/646 (77.4%) - 1.67 items/sec - ETA: 1m 27s
2025-05-30 21:50:36 - ebook_indexer.core.indexer.EbookIndexer - INFO - Indexing 1 directories (skipped 4 already indexed): 600/646 (92.9%) - 1.63 items/sec - ETA: 0m 28s
2025-05-30 21:51:04 - ebook_indexer.utils.progress_tracker.ProgressTracker - INFO - Completed: Indexing 1 directories (skipped 4 already indexed) - 646/646 items (100.0%) in 0:06:35.462121 (1.63 items/sec)
2025-05-30 21:51:17 - ebook_indexer.core.indexer.EbookIndexer - WARNING - Metadata extraction failed for theory-structured-parallel-programming.epub: Error reading EPUB metadata: "There is no item named 'OEBPS/images/B9780443248146000095/fx002.jpg' in the archive"
2025-05-30 21:51:19 - ebook_indexer.core.indexer.EbookIndexer - INFO - Job 683a0a9486468a8490a8d797 completed: 671/671 files successful, 0 failed, 21 with anomalies
2025-05-30 21:53:33 - ebook_indexer.core.scanner.DirectoryScanner - INFO - Scanner initialized with extensions: ['.pdf', '.epub', '.mobi', '.azw', '.azw3', '.txt']
2025-05-30 21:53:33 - ebook_indexer.core.metadata_extractor.MetadataExtractor - INFO - Metadata extractor initialized for formats: ['.pdf', '.epub']
2025-05-30 21:53:33 - ebook_indexer.core.anomaly_detector.AnomalyDetector - INFO - Anomaly detector initialized with config: {'max_nesting_depth': 4, 'enforce_naming_convention': True, 'detect_misplaced_files': True, 'severity_thresholds': {'wrong_level': 'medium', 'deep_nesting': 'low', 'missing_directory': 'high', 'naming_violation': 'low'}}
2025-05-30 21:53:33 - ebook_indexer.core.indexer.EbookIndexer - INFO - Ebook indexer initialized
2025-05-30 21:53:33 - ebook_indexer.core.indexer.EbookIndexer - INFO - Skipping 5 already indexed directories: ['/run/user/1000/gvfs/smb-share:server=**************,share=dmedia6/scanlibs-ebooks-202402', '/run/user/1000/gvfs/smb-share:server=**************,share=dmedia6/scanlibs-ebooks-202403', '/run/user/1000/gvfs/smb-share:server=**************,share=dmedia6/scanlibs-ebooks-202404', '/run/user/1000/gvfs/smb-share:server=**************,share=dmedia6/scanlibs-ebooks-202405', '/run/user/1000/gvfs/smb-share:server=**************,share=dmedia6/scanlibs-ebooks-202406']
2025-05-30 21:53:33 - ebook_indexer.core.indexer.EbookIndexer - INFO - Use --force flag to reindex these directories
2025-05-30 21:53:33 - ebook_indexer.core.indexer.EbookIndexer - INFO - Created new job 683a0cbd86468a8490a8dcd8
2025-05-30 21:53:33 - ebook_indexer.core.indexer.EbookIndexer - INFO - Starting indexing job 683a0cbd86468a8490a8dcd8 for directories: ['/run/user/1000/gvfs/smb-share:server=**************,share=dmedia6/scanlibs-ebooks-202407']
2025-05-30 21:53:39 - ebook_indexer.core.indexer.EbookIndexer - INFO - Found 432 files to process
2025-05-30 21:53:39 - ebook_indexer.utils.progress_tracker.ProgressTracker - INFO - Progress tracker initialized: Indexing 1 directories (skipped 5 already indexed) (432 items)
2025-05-30 21:53:39 - ebook_indexer.core.scanner.DirectoryScanner - INFO - Scanning directory: /run/user/1000/gvfs/smb-share:server=**************,share=dmedia6/scanlibs-ebooks-202407
2025-05-30 21:53:39 - ebook_indexer.core.scanner.DirectoryScanner - INFO - Starting scan of directory: /run/user/1000/gvfs/smb-share:server=**************,share=dmedia6/scanlibs-ebooks-202407
2025-05-30 21:53:40 - ebook_indexer.utils.progress_tracker.ProgressTracker - INFO - Started: Indexing 1 directories (skipped 5 already indexed)
2025-05-30 21:53:40 - ebook_indexer.core.indexer.EbookIndexer - INFO - Indexing 1 directories (skipped 5 already indexed): 0/432 (0.0%)
2025-05-30 21:53:57 - ebook_indexer.core.indexer.EbookIndexer - INFO - Indexing 1 directories (skipped 5 already indexed): 50/432 (11.6%) - 2.83 items/sec - ETA: 2m 14s
2025-05-30 21:54:04 - ebook_indexer.core.indexer.EbookIndexer - WARNING - Metadata extraction failed for api-design-cpp-2nd.pdf: Error reading PDF metadata: unhashable type: 'ArrayObject'
2025-05-30 21:54:34 - ebook_indexer.core.indexer.EbookIndexer - INFO - Indexing 1 directories (skipped 5 already indexed): 100/432 (23.1%) - 1.83 items/sec - ETA: 3m 0s
