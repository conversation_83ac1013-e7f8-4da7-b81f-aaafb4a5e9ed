2025-05-30 17:23:12 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-30 17:23:17 - ebook_indexer.database.connection.MongoDBConnection - ERROR - Failed to connect to MongoDB: localhost:27017: [Errno 104] Connection reset by peer (configured timeouts: socketTimeoutMS: 5000.0ms, connectTimeoutMS: 5000.0ms), Timeout: 5.0s, Topology Description: <TopologyDescription id: 6839cd604ca2e907c537093e, topology_type: Unknown, servers: [<ServerDescription ('localhost', 27017) server_type: Unknown, rtt: None, error=AutoReconnect('localhost:27017: [Errno 104] Connection reset by peer (configured timeouts: socketTimeoutMS: 5000.0ms, connectTimeoutMS: 5000.0ms)')>]>
2025-05-30 17:23:17 - ebook_indexer.database.connection.MongoDBConnection - WARNING - MongoDB health check failed: Failed to connect to MongoDB: localhost:27017: [<PERSON>rrno 104] Connection reset by peer (configured timeouts: socketTimeoutMS: 5000.0ms, connectTimeoutMS: 5000.0ms), Timeout: 5.0s, Topology Description: <TopologyDescription id: 6839cd604ca2e907c537093e, topology_type: Unknown, servers: [<ServerDescription ('localhost', 27017) server_type: Unknown, rtt: None, error=AutoReconnect('localhost:27017: [Errno 104] Connection reset by peer (configured timeouts: socketTimeoutMS: 5000.0ms, connectTimeoutMS: 5000.0ms)')>]>
2025-05-30 17:23:24 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-30 17:23:24 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-30 17:23:32 - ebook_indexer.core.scanner.DirectoryScanner - INFO - Scanner initialized with extensions: ['.pdf', '.epub', '.mobi', '.azw', '.azw3']
2025-05-30 17:23:32 - ebook_indexer.core.metadata_extractor.MetadataExtractor - INFO - Metadata extractor initialized for formats: ['.pdf', '.epub']
2025-05-30 17:23:32 - ebook_indexer.core.anomaly_detector.AnomalyDetector - INFO - Anomaly detector initialized with config: {'max_nesting_depth': 4, 'enforce_naming_convention': True, 'detect_misplaced_files': True, 'severity_thresholds': {'wrong_level': 'medium', 'deep_nesting': 'low', 'missing_directory': 'high', 'naming_violation': 'low'}}
2025-05-30 17:23:32 - ebook_indexer.core.indexer.EbookIndexer - INFO - Ebook indexer initialized
2025-05-30 17:32:30 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-30 17:32:30 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-30 17:41:09 - ebook_indexer.database.connection.MongoDBConnection - INFO - Disconnecting from MongoDB
2025-05-30 17:41:21 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-30 17:41:21 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-30 17:47:17 - ebook_indexer.database.connection.MongoDBConnection - INFO - Disconnecting from MongoDB
2025-05-30 17:47:17 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-30 17:47:17 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-30 17:47:31 - ebook_indexer.database.connection.MongoDBConnection - INFO - Disconnecting from MongoDB
2025-05-30 17:47:31 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-30 17:47:31 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-30 17:47:38 - ebook_indexer.database.connection.MongoDBConnection - INFO - Disconnecting from MongoDB
2025-05-30 17:47:39 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-30 17:47:39 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-30 17:48:04 - ebook_indexer.database.connection.MongoDBConnection - INFO - Disconnecting from MongoDB
2025-05-30 17:48:04 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-30 17:48:04 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-30 17:48:12 - ebook_indexer.database.connection.MongoDBConnection - INFO - Disconnecting from MongoDB
2025-05-30 17:48:12 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-30 17:48:12 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-30 17:48:22 - ebook_indexer.database.connection.MongoDBConnection - INFO - Disconnecting from MongoDB
2025-05-30 17:48:22 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-30 17:48:22 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-30 17:48:50 - ebook_indexer.database.connection.MongoDBConnection - INFO - Disconnecting from MongoDB
2025-05-30 17:48:50 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-30 17:48:50 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-30 17:49:53 - ebook_indexer.database.connection.MongoDBConnection - INFO - Disconnecting from MongoDB
2025-05-30 17:49:54 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-30 17:49:54 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-30 17:50:15 - ebook_indexer.database.connection.MongoDBConnection - INFO - Disconnecting from MongoDB
2025-05-30 17:50:15 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-30 17:50:15 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-30 17:50:26 - ebook_indexer.database.connection.MongoDBConnection - INFO - Disconnecting from MongoDB
2025-05-30 17:50:26 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-30 17:50:26 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-30 17:50:41 - ebook_indexer.database.connection.MongoDBConnection - INFO - Disconnecting from MongoDB
2025-05-30 17:50:41 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-30 17:50:41 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-30 17:50:48 - ebook_indexer.database.connection.MongoDBConnection - INFO - Disconnecting from MongoDB
2025-05-30 17:50:49 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-30 17:50:49 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-30 17:51:14 - ebook_indexer.database.connection.MongoDBConnection - INFO - Disconnecting from MongoDB
2025-05-30 17:51:14 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-30 17:51:14 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-30 17:51:32 - ebook_indexer.database.connection.MongoDBConnection - INFO - Disconnecting from MongoDB
2025-05-30 17:51:32 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-30 17:51:32 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-30 17:51:49 - ebook_indexer.database.connection.MongoDBConnection - INFO - Disconnecting from MongoDB
2025-05-30 17:51:50 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-30 17:51:50 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-30 17:51:58 - ebook_indexer.database.connection.MongoDBConnection - INFO - Disconnecting from MongoDB
2025-05-30 17:51:59 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-30 17:51:59 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-30 17:52:20 - ebook_indexer.database.connection.MongoDBConnection - INFO - Disconnecting from MongoDB
2025-05-30 17:52:20 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-30 17:52:20 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-30 17:52:42 - ebook_indexer.database.connection.MongoDBConnection - INFO - Disconnecting from MongoDB
2025-05-30 17:52:43 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-30 17:52:43 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-30 17:52:59 - ebook_indexer.database.connection.MongoDBConnection - INFO - Disconnecting from MongoDB
2025-05-30 17:52:59 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-30 17:52:59 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-30 17:53:37 - ebook_indexer.database.connection.MongoDBConnection - INFO - Disconnecting from MongoDB
2025-05-30 17:53:37 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-30 17:53:37 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-30 17:53:56 - ebook_indexer.database.connection.MongoDBConnection - INFO - Disconnecting from MongoDB
2025-05-30 17:53:56 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-30 17:53:56 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-30 17:55:10 - ebook_indexer.database.connection.MongoDBConnection - INFO - Disconnecting from MongoDB
2025-05-30 17:55:10 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-30 17:55:10 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-30 17:55:21 - ebook_indexer.database.connection.MongoDBConnection - INFO - Disconnecting from MongoDB
2025-05-30 17:55:21 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-30 17:55:21 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-30 17:55:29 - ebook_indexer.database.connection.MongoDBConnection - INFO - Disconnecting from MongoDB
2025-05-30 17:55:30 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-30 17:55:30 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-30 17:56:12 - ebook_indexer.database.connection.MongoDBConnection - INFO - Disconnecting from MongoDB
2025-05-30 17:56:12 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-30 17:56:12 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-30 17:56:49 - ebook_indexer.database.connection.MongoDBConnection - INFO - Disconnecting from MongoDB
2025-05-30 17:56:49 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-30 17:56:49 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-30 17:57:21 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-30 17:57:21 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-30 17:57:21 - ebook_indexer.database.connection.MongoDBConnection - INFO - Disconnecting from MongoDB
2025-05-30 17:59:01 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-30 17:59:01 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-30 18:00:31 - ebook_indexer.core.scanner.DirectoryScanner - INFO - Scanner initialized with extensions: ['.pdf', '.epub', '.mobi', '.azw', '.azw3', '.txt']
2025-05-30 18:00:31 - ebook_indexer.core.metadata_extractor.MetadataExtractor - INFO - Metadata extractor initialized for formats: ['.pdf', '.epub']
2025-05-30 18:00:31 - ebook_indexer.core.anomaly_detector.AnomalyDetector - INFO - Anomaly detector initialized with config: {'max_nesting_depth': 4, 'enforce_naming_convention': True, 'detect_misplaced_files': True, 'severity_thresholds': {'wrong_level': 'medium', 'deep_nesting': 'low', 'missing_directory': 'high', 'naming_violation': 'low'}}
2025-05-30 18:00:31 - ebook_indexer.core.indexer.EbookIndexer - INFO - Ebook indexer initialized
2025-05-30 18:01:13 - ebook_indexer.database.connection.MongoDBConnection - INFO - Disconnecting from MongoDB
2025-05-30 18:01:14 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-30 18:01:14 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-30 18:03:27 - ebook_indexer.database.connection.MongoDBConnection - INFO - Disconnecting from MongoDB
2025-05-30 18:03:28 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-30 18:03:28 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-30 18:03:35 - ebook_indexer.core.scanner.DirectoryScanner - INFO - Scanner initialized with extensions: ['.pdf', '.epub', '.mobi', '.azw', '.azw3', '.txt']
2025-05-30 18:03:35 - ebook_indexer.core.metadata_extractor.MetadataExtractor - INFO - Metadata extractor initialized for formats: ['.pdf', '.epub']
2025-05-30 18:03:35 - ebook_indexer.core.anomaly_detector.AnomalyDetector - INFO - Anomaly detector initialized with config: {'max_nesting_depth': 4, 'enforce_naming_convention': True, 'detect_misplaced_files': True, 'severity_thresholds': {'wrong_level': 'medium', 'deep_nesting': 'low', 'missing_directory': 'high', 'naming_violation': 'low'}}
2025-05-30 18:03:35 - ebook_indexer.core.indexer.EbookIndexer - INFO - Ebook indexer initialized
