2025-05-30 17:23:12 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-30 17:23:17 - ebook_indexer.database.connection.MongoDBConnection - ERROR - Failed to connect to MongoDB: localhost:27017: [Errno 104] Connection reset by peer (configured timeouts: socketTimeoutMS: 5000.0ms, connectTimeoutMS: 5000.0ms), Timeout: 5.0s, Topology Description: <TopologyDescription id: 6839cd604ca2e907c537093e, topology_type: Unknown, servers: [<ServerDescription ('localhost', 27017) server_type: Unknown, rtt: None, error=AutoReconnect('localhost:27017: [Errno 104] Connection reset by peer (configured timeouts: socketTimeoutMS: 5000.0ms, connectTimeoutMS: 5000.0ms)')>]>
2025-05-30 17:23:17 - ebook_indexer.database.connection.MongoDBConnection - WARNING - MongoDB health check failed: Failed to connect to MongoDB: localhost:27017: [<PERSON>rrno 104] Connection reset by peer (configured timeouts: socketTimeoutMS: 5000.0ms, connectTimeoutMS: 5000.0ms), Timeout: 5.0s, Topology Description: <TopologyDescription id: 6839cd604ca2e907c537093e, topology_type: Unknown, servers: [<ServerDescription ('localhost', 27017) server_type: Unknown, rtt: None, error=AutoReconnect('localhost:27017: [Errno 104] Connection reset by peer (configured timeouts: socketTimeoutMS: 5000.0ms, connectTimeoutMS: 5000.0ms)')>]>
2025-05-30 17:23:24 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-30 17:23:24 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-30 17:23:32 - ebook_indexer.core.scanner.DirectoryScanner - INFO - Scanner initialized with extensions: ['.pdf', '.epub', '.mobi', '.azw', '.azw3']
2025-05-30 17:23:32 - ebook_indexer.core.metadata_extractor.MetadataExtractor - INFO - Metadata extractor initialized for formats: ['.pdf', '.epub']
2025-05-30 17:23:32 - ebook_indexer.core.anomaly_detector.AnomalyDetector - INFO - Anomaly detector initialized with config: {'max_nesting_depth': 4, 'enforce_naming_convention': True, 'detect_misplaced_files': True, 'severity_thresholds': {'wrong_level': 'medium', 'deep_nesting': 'low', 'missing_directory': 'high', 'naming_violation': 'low'}}
2025-05-30 17:23:32 - ebook_indexer.core.indexer.EbookIndexer - INFO - Ebook indexer initialized
2025-05-30 17:32:30 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-30 17:32:30 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-30 17:41:09 - ebook_indexer.database.connection.MongoDBConnection - INFO - Disconnecting from MongoDB
2025-05-30 17:41:21 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-30 17:41:21 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-30 17:47:17 - ebook_indexer.database.connection.MongoDBConnection - INFO - Disconnecting from MongoDB
2025-05-30 17:47:17 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-30 17:47:17 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-30 17:47:31 - ebook_indexer.database.connection.MongoDBConnection - INFO - Disconnecting from MongoDB
2025-05-30 17:47:31 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-30 17:47:31 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-30 17:47:38 - ebook_indexer.database.connection.MongoDBConnection - INFO - Disconnecting from MongoDB
2025-05-30 17:47:39 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-30 17:47:39 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-30 17:48:04 - ebook_indexer.database.connection.MongoDBConnection - INFO - Disconnecting from MongoDB
2025-05-30 17:48:04 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-30 17:48:04 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-30 17:48:12 - ebook_indexer.database.connection.MongoDBConnection - INFO - Disconnecting from MongoDB
2025-05-30 17:48:12 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-30 17:48:12 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-30 17:48:22 - ebook_indexer.database.connection.MongoDBConnection - INFO - Disconnecting from MongoDB
2025-05-30 17:48:22 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-30 17:48:22 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-30 17:48:50 - ebook_indexer.database.connection.MongoDBConnection - INFO - Disconnecting from MongoDB
2025-05-30 17:48:50 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-30 17:48:50 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-30 17:49:53 - ebook_indexer.database.connection.MongoDBConnection - INFO - Disconnecting from MongoDB
2025-05-30 17:49:54 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-30 17:49:54 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-30 17:50:15 - ebook_indexer.database.connection.MongoDBConnection - INFO - Disconnecting from MongoDB
2025-05-30 17:50:15 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-30 17:50:15 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-30 17:50:26 - ebook_indexer.database.connection.MongoDBConnection - INFO - Disconnecting from MongoDB
2025-05-30 17:50:26 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-30 17:50:26 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-30 17:50:41 - ebook_indexer.database.connection.MongoDBConnection - INFO - Disconnecting from MongoDB
2025-05-30 17:50:41 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-30 17:50:41 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-30 17:50:48 - ebook_indexer.database.connection.MongoDBConnection - INFO - Disconnecting from MongoDB
2025-05-30 17:50:49 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-30 17:50:49 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-30 17:51:14 - ebook_indexer.database.connection.MongoDBConnection - INFO - Disconnecting from MongoDB
2025-05-30 17:51:14 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-30 17:51:14 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-30 17:51:32 - ebook_indexer.database.connection.MongoDBConnection - INFO - Disconnecting from MongoDB
2025-05-30 17:51:32 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-30 17:51:32 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-30 17:51:49 - ebook_indexer.database.connection.MongoDBConnection - INFO - Disconnecting from MongoDB
2025-05-30 17:51:50 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-30 17:51:50 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-30 17:51:58 - ebook_indexer.database.connection.MongoDBConnection - INFO - Disconnecting from MongoDB
2025-05-30 17:51:59 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-30 17:51:59 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-30 17:52:20 - ebook_indexer.database.connection.MongoDBConnection - INFO - Disconnecting from MongoDB
2025-05-30 17:52:20 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-30 17:52:20 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-30 17:52:42 - ebook_indexer.database.connection.MongoDBConnection - INFO - Disconnecting from MongoDB
2025-05-30 17:52:43 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-30 17:52:43 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-30 17:52:59 - ebook_indexer.database.connection.MongoDBConnection - INFO - Disconnecting from MongoDB
2025-05-30 17:52:59 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-30 17:52:59 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-30 17:53:37 - ebook_indexer.database.connection.MongoDBConnection - INFO - Disconnecting from MongoDB
2025-05-30 17:53:37 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-30 17:53:37 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-30 17:53:56 - ebook_indexer.database.connection.MongoDBConnection - INFO - Disconnecting from MongoDB
2025-05-30 17:53:56 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-30 17:53:56 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-30 17:55:10 - ebook_indexer.database.connection.MongoDBConnection - INFO - Disconnecting from MongoDB
2025-05-30 17:55:10 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-30 17:55:10 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-30 17:55:21 - ebook_indexer.database.connection.MongoDBConnection - INFO - Disconnecting from MongoDB
2025-05-30 17:55:21 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-30 17:55:21 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-30 17:55:29 - ebook_indexer.database.connection.MongoDBConnection - INFO - Disconnecting from MongoDB
2025-05-30 17:55:30 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-30 17:55:30 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-30 17:56:12 - ebook_indexer.database.connection.MongoDBConnection - INFO - Disconnecting from MongoDB
2025-05-30 17:56:12 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-30 17:56:12 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-30 17:56:49 - ebook_indexer.database.connection.MongoDBConnection - INFO - Disconnecting from MongoDB
2025-05-30 17:56:49 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-30 17:56:49 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-30 17:57:21 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-30 17:57:21 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-30 17:57:21 - ebook_indexer.database.connection.MongoDBConnection - INFO - Disconnecting from MongoDB
2025-05-30 17:59:01 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-30 17:59:01 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-30 18:00:31 - ebook_indexer.core.scanner.DirectoryScanner - INFO - Scanner initialized with extensions: ['.pdf', '.epub', '.mobi', '.azw', '.azw3', '.txt']
2025-05-30 18:00:31 - ebook_indexer.core.metadata_extractor.MetadataExtractor - INFO - Metadata extractor initialized for formats: ['.pdf', '.epub']
2025-05-30 18:00:31 - ebook_indexer.core.anomaly_detector.AnomalyDetector - INFO - Anomaly detector initialized with config: {'max_nesting_depth': 4, 'enforce_naming_convention': True, 'detect_misplaced_files': True, 'severity_thresholds': {'wrong_level': 'medium', 'deep_nesting': 'low', 'missing_directory': 'high', 'naming_violation': 'low'}}
2025-05-30 18:00:31 - ebook_indexer.core.indexer.EbookIndexer - INFO - Ebook indexer initialized
2025-05-30 18:01:13 - ebook_indexer.database.connection.MongoDBConnection - INFO - Disconnecting from MongoDB
2025-05-30 18:01:14 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-30 18:01:14 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-30 18:03:27 - ebook_indexer.database.connection.MongoDBConnection - INFO - Disconnecting from MongoDB
2025-05-30 18:03:28 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-30 18:03:28 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-30 18:03:35 - ebook_indexer.core.scanner.DirectoryScanner - INFO - Scanner initialized with extensions: ['.pdf', '.epub', '.mobi', '.azw', '.azw3', '.txt']
2025-05-30 18:03:35 - ebook_indexer.core.metadata_extractor.MetadataExtractor - INFO - Metadata extractor initialized for formats: ['.pdf', '.epub']
2025-05-30 18:03:35 - ebook_indexer.core.anomaly_detector.AnomalyDetector - INFO - Anomaly detector initialized with config: {'max_nesting_depth': 4, 'enforce_naming_convention': True, 'detect_misplaced_files': True, 'severity_thresholds': {'wrong_level': 'medium', 'deep_nesting': 'low', 'missing_directory': 'high', 'naming_violation': 'low'}}
2025-05-30 18:03:35 - ebook_indexer.core.indexer.EbookIndexer - INFO - Ebook indexer initialized
2025-05-30 18:07:20 - ebook_indexer.database.connection.MongoDBConnection - INFO - Disconnecting from MongoDB
2025-05-30 18:07:22 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-30 18:07:22 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-30 18:07:49 - ebook_indexer.core.scanner.DirectoryScanner - INFO - Scanner initialized with extensions: ['.pdf', '.epub', '.mobi', '.azw', '.azw3', '.txt']
2025-05-30 18:07:49 - ebook_indexer.core.metadata_extractor.MetadataExtractor - INFO - Metadata extractor initialized for formats: ['.pdf', '.epub']
2025-05-30 18:07:49 - ebook_indexer.core.anomaly_detector.AnomalyDetector - INFO - Anomaly detector initialized with config: {'max_nesting_depth': 4, 'enforce_naming_convention': True, 'detect_misplaced_files': True, 'severity_thresholds': {'wrong_level': 'medium', 'deep_nesting': 'low', 'missing_directory': 'high', 'naming_violation': 'low'}}
2025-05-30 18:07:49 - ebook_indexer.core.indexer.EbookIndexer - INFO - Ebook indexer initialized
2025-05-30 18:07:49 - ebook_indexer.core.indexer.EbookIndexer - INFO - Skipping 3 already indexed directories: ['/run/user/1000/gvfs/smb-share:server=**************,share=dmedia6/scanlibs-ebooks-202402', '/run/user/1000/gvfs/smb-share:server=**************,share=dmedia6/scanlibs-ebooks-202403', '/run/user/1000/gvfs/smb-share:server=**************,share=dmedia6/scanlibs-ebooks-202404']
2025-05-30 18:07:49 - ebook_indexer.core.indexer.EbookIndexer - INFO - Use --force flag to reindex these directories
2025-05-30 18:07:49 - ebook_indexer.core.indexer.EbookIndexer - INFO - Created new job 6839d7d5dced5e6b6b85f6af
2025-05-30 18:07:49 - ebook_indexer.core.indexer.EbookIndexer - INFO - Starting indexing job 6839d7d5dced5e6b6b85f6af for directories: ['/run/user/1000/gvfs/smb-share:server=**************,share=dmedia6/scanlibs-ebooks-202405']
2025-05-30 18:07:55 - ebook_indexer.core.indexer.EbookIndexer - INFO - Found 376 files to process
2025-05-30 18:07:55 - ebook_indexer.utils.progress_tracker.ProgressTracker - INFO - Progress tracker initialized: Indexing 1 directories (skipped 3 already indexed) (376 items)
2025-05-30 18:07:55 - ebook_indexer.core.scanner.DirectoryScanner - INFO - Scanning directory: /run/user/1000/gvfs/smb-share:server=**************,share=dmedia6/scanlibs-ebooks-202405
2025-05-30 18:07:55 - ebook_indexer.core.scanner.DirectoryScanner - INFO - Starting scan of directory: /run/user/1000/gvfs/smb-share:server=**************,share=dmedia6/scanlibs-ebooks-202405
2025-05-30 18:07:56 - ebook_indexer.utils.progress_tracker.ProgressTracker - INFO - Started: Indexing 1 directories (skipped 3 already indexed)
2025-05-30 18:07:56 - ebook_indexer.core.indexer.EbookIndexer - INFO - Indexing 1 directories (skipped 3 already indexed): 0/376 (0.0%)
2025-05-30 18:08:33 - ebook_indexer.core.indexer.EbookIndexer - INFO - Indexing 1 directories (skipped 3 already indexed): 50/376 (13.3%) - 1.35 items/sec - ETA: 4m 2s
2025-05-30 18:09:15 - ebook_indexer.core.indexer.EbookIndexer - INFO - Indexing 1 directories (skipped 3 already indexed): 100/376 (26.6%) - 1.26 items/sec - ETA: 3m 38s
2025-05-30 18:10:20 - ebook_indexer.core.indexer.EbookIndexer - INFO - Indexing 1 directories (skipped 3 already indexed): 250/376 (66.5%) - 1.73 items/sec - ETA: 1m 12s
2025-05-30 18:10:26 - ebook_indexer.core.indexer.EbookIndexer - WARNING - Metadata extraction failed for foundations-quantum-programming-2nd.epub: Error reading EPUB metadata: "There is no item named 'OEBPS/images/B9780443159428000095/si10.png' in the archive"
2025-05-30 18:11:23 - ebook_indexer.core.indexer.EbookIndexer - INFO - Indexing 1 directories (skipped 3 already indexed): 350/376 (93.1%) - 1.69 items/sec - ETA: 0m 15s
2025-05-30 18:11:36 - ebook_indexer.utils.progress_tracker.ProgressTracker - INFO - Completed: Indexing 1 directories (skipped 3 already indexed) - 376/376 items (100.0%) in 0:03:40.533881 (1.70 items/sec)
2025-05-30 18:11:43 - ebook_indexer.core.indexer.EbookIndexer - WARNING - Metadata extraction failed for geophysical-analysis-matlab-python-5th.pdf: Error reading PDF metadata: unhashable type: 'ArrayObject'
2025-05-30 18:11:50 - ebook_indexer.core.indexer.EbookIndexer - INFO - Indexing 1 directories (skipped 3 already indexed): 400/376 (106.4%) - 1.71 items/sec
2025-05-30 18:12:12 - ebook_indexer.core.indexer.EbookIndexer - INFO - Job 6839d7d5dced5e6b6b85f6af completed: 431/431 files successful, 0 failed, 59 with anomalies
