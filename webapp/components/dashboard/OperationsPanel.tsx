import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { OperationStatus, getRootDirectories } from "@/lib/api";
import { useOperations } from "@/hooks/useOperations";
import { getStatusColor, formatDuration, formatDate } from "@/lib/utils";
import { 
  Play, 
  Square, 
  RefreshCw, 
  FolderPlus, 
  AlertCircle, 
  CheckCircle,
  Clock,
  Loader2
} from "lucide-react";

interface OperationsPanelProps {
  activeOperations: OperationStatus[];
}

export function OperationsPanel({ activeOperations }: OperationsPanelProps) {
  const { 
    operations, 
    loading, 
    error, 
    startRetryOperation, 
    startIndexOperation, 
    cancelOperation 
  } = useOperations();
  
  const [actionLoading, setActionLoading] = useState<string | null>(null);
  const [actionError, setActionError] = useState<string | null>(null);

  const handleRetryOperation = async (jobId?: string) => {
    try {
      setActionLoading('retry');
      setActionError(null);
      await startRetryOperation(jobId);
    } catch (err) {
      console.error('Failed to start retry operation:', err);
      setActionError(err instanceof Error ? err.message : 'Failed to start retry operation');
    } finally {
      setActionLoading(null);
    }
  };

  const handleIndexOperation = async () => {
    try {
      setActionLoading('index');
      setActionError(null);

      // Fetch current root directories from the API
      const rootDirsResponse = await getRootDirectories();
      const rootDirectoryPaths = rootDirsResponse.root_directories.map(dir => dir.path);

      if (rootDirectoryPaths.length === 0) {
        throw new Error('No root directories configured. Please add at least one root directory before starting indexing.');
      }

      await startIndexOperation(rootDirectoryPaths);
    } catch (err) {
      console.error('Failed to start index operation:', err);
      setActionError(err instanceof Error ? err.message : 'Failed to start index operation');
    } finally {
      setActionLoading(null);
    }
  };

  const handleCancelOperation = async (operationId: string) => {
    try {
      setActionLoading(`cancel-${operationId}`);
      await cancelOperation(operationId);
    } catch (err) {
      console.error('Failed to cancel operation:', err);
    } finally {
      setActionLoading(null);
    }
  };

  const getOperationIcon = (type: string, status: string) => {
    if (status === 'running') return <Loader2 className="h-4 w-4 animate-spin" />;
    if (status === 'completed') return <CheckCircle className="h-4 w-4 text-green-600" />;
    if (status === 'failed') return <AlertCircle className="h-4 w-4 text-red-600" />;
    if (type === 'retry') return <RefreshCw className="h-4 w-4" />;
    if (type === 'index') return <FolderPlus className="h-4 w-4" />;
    return <Clock className="h-4 w-4" />;
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span>Operations</span>
          <div className="flex gap-2">
            <Button
              size="sm"
              variant="outline"
              onClick={() => handleRetryOperation()}
              disabled={actionLoading === 'retry'}
            >
              {actionLoading === 'retry' ? (
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
              ) : (
                <RefreshCw className="h-4 w-4 mr-2" />
              )}
              Retry Failed
            </Button>
            <Button
              size="sm"
              onClick={() => handleIndexOperation()}
              disabled={actionLoading === 'index'}
            >
              {actionLoading === 'index' ? (
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
              ) : (
                <Play className="h-4 w-4 mr-2" />
              )}
              Start Indexing
            </Button>
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent>
        {(error || actionError) && (
          <Alert variant="destructive" className="mb-4">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{actionError || error}</AlertDescription>
          </Alert>
        )}

        {loading ? (
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-6 w-6 animate-spin" />
          </div>
        ) : operations.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            No operations found
          </div>
        ) : (
          <div className="space-y-4">
            {operations.slice(0, 5).map((operation) => (
              <div key={operation.operation_id} className="border rounded-lg p-4">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-2">
                    {getOperationIcon(operation.type, operation.status)}
                    <span className="font-medium capitalize">{operation.type}</span>
                    <Badge className={getStatusColor(operation.status)}>
                      {operation.status}
                    </Badge>
                  </div>
                  {operation.status === 'running' && (
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleCancelOperation(operation.operation_id)}
                      disabled={actionLoading === `cancel-${operation.operation_id}`}
                    >
                      {actionLoading === `cancel-${operation.operation_id}` ? (
                        <Loader2 className="h-3 w-3 animate-spin" />
                      ) : (
                        <Square className="h-3 w-3" />
                      )}
                    </Button>
                  )}
                </div>

                {operation.status === 'running' && (
                  <div className="space-y-2">
                    <Progress value={operation.progress.percentage} />
                    <div className="flex justify-between text-xs text-muted-foreground">
                      <span>
                        {operation.progress.processed} / {operation.progress.total} files
                      </span>
                      <span>{operation.progress.percentage.toFixed(1)}%</span>
                    </div>
                    {operation.progress.eta_seconds && (
                      <div className="text-xs text-muted-foreground">
                        ETA: {formatDuration(operation.progress.eta_seconds)}
                      </div>
                    )}
                  </div>
                )}

                {operation.status === 'completed' && (
                  <div className="text-xs text-muted-foreground space-y-1">
                    <div>✅ Successful: {operation.results.successful}</div>
                    <div>❌ Failed: {operation.results.failed}</div>
                    {operation.results.still_anomalous > 0 && (
                      <div>⚠️ Still anomalous: {operation.results.still_anomalous}</div>
                    )}
                  </div>
                )}

                <div className="text-xs text-muted-foreground mt-2">
                  Started: {formatDate(operation.started_at)}
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
